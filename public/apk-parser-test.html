<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>APK 解析测试页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/app-info-parser/dist/app-info-parser.min.js"></script>
    <style>
        .loader {
            border: 4px solid #f3f4f6;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="max-w-4xl mx-auto px-4">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">APK 解析测试</h1>
            
            <!-- 文件上传区域 -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">选择 APK 文件</label>
                <input type="file" id="apk-input" accept=".apk" 
                       class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
            </div>
            
            <!-- 加载状态 -->
            <div id="loading" class="hidden flex items-center justify-center py-8">
                <div class="loader"></div>
                <span class="ml-3 text-gray-600">正在解析 APK...</span>
            </div>
            
            <!-- 解析结果 -->
            <div id="result" class="hidden">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">解析结果</h2>
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">应用名称</label>
                            <div id="app-label" class="mt-1 text-sm text-gray-900"></div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">包名</label>
                            <div id="app-package" class="mt-1 text-sm text-gray-900"></div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">版本名称</label>
                            <div id="app-version-name" class="mt-1 text-sm text-gray-900 font-semibold text-blue-600"></div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">版本号</label>
                            <div id="app-version-code" class="mt-1 text-sm text-gray-900 font-semibold text-blue-600"></div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">最小 SDK</label>
                            <div id="app-min-sdk" class="mt-1 text-sm text-gray-900"></div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">目标 SDK</label>
                            <div id="app-target-sdk" class="mt-1 text-sm text-gray-900"></div>
                        </div>
                    </div>
                    
                    <!-- 应用图标 -->
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">应用图标</label>
                        <img id="app-icon" class="w-16 h-16 rounded-lg border" style="display: none;">
                    </div>
                </div>
            </div>
            
            <!-- 错误信息 -->
            <div id="error" class="hidden bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">解析失败</h3>
                        <div id="error-message" class="mt-2 text-sm text-red-700"></div>
                    </div>
                </div>
            </div>
            
            <!-- 测试说明 -->
            <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 class="text-sm font-medium text-blue-800 mb-2">测试说明</h3>
                <ul class="text-sm text-blue-700 space-y-1">
                    <li>• 选择一个 APK 文件进行解析测试</li>
                    <li>• 系统会自动提取版本名称和版本号</li>
                    <li>• 解析成功后，这些信息可以自动填充到发布表单中</li>
                    <li>• 支持的文件名格式：MyApp-v1.2.3.apk, App_2.0.1_build123.apk 等</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('apk-input').addEventListener('change', async (e) => {
            const file = e.target.files[0];
            if (!file) return;
            
            // 显示加载状态
            document.getElementById('loading').classList.remove('hidden');
            document.getElementById('result').classList.add('hidden');
            document.getElementById('error').classList.add('hidden');
            
            try {
                // 使用 app-info-parser 解析 APK
                const parser = new AppInfoParser(file);
                const result = await parser.parse();
                
                console.log('解析结果:', result);
                
                // 显示结果
                document.getElementById('app-label').textContent = result.label || 'N/A';
                document.getElementById('app-package').textContent = result.package || 'N/A';
                document.getElementById('app-version-name').textContent = result.versionName || 'N/A';
                document.getElementById('app-version-code').textContent = result.versionCode || 'N/A';
                document.getElementById('app-min-sdk').textContent = result.usesSdk?.minSdkVersion || 'N/A';
                document.getElementById('app-target-sdk').textContent = result.usesSdk?.targetSdkVersion || 'N/A';
                
                // 显示图标
                if (result.icon) {
                    const iconImg = document.getElementById('app-icon');
                    iconImg.src = result.icon;
                    iconImg.style.display = 'block';
                }
                
                document.getElementById('loading').classList.add('hidden');
                document.getElementById('result').classList.remove('hidden');
                
            } catch (error) {
                console.error('解析失败:', error);
                
                document.getElementById('error-message').textContent = error.message;
                document.getElementById('loading').classList.add('hidden');
                document.getElementById('error').classList.remove('hidden');
            }
        });
    </script>
</body>
</html>
