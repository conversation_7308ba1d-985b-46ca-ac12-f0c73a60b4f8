<template>
  <div class="file-upload-container">
    <a-upload
      :custom-request="handleUpload"
      :show-file-list="false"
      :accept="accept"
      :disabled="uploading"
      @before-upload="beforeUpload"
    >
      <template #upload-button>
        <div
          :class="[
            'upload-area',
            'border-2 border-dashed rounded-lg p-6 text-center transition-colors duration-200 min-h-[160px] flex flex-col justify-center',
            uploading ? 'uploading border-blue-300 bg-blue-50' : 'border-gray-300 hover:border-blue-400 hover:bg-gray-50',
            isDark && !uploading ? 'border-gray-600 hover:border-blue-500 hover:bg-gray-800/50' : '',
            isDark && uploading ? 'border-blue-400 bg-blue-900/20' : ''
          ]"
        >
          <div v-if="!uploading" class="upload-content">
            <div :class="['upload-icon mb-3', isDark ? 'text-gray-400' : 'text-gray-500']">
              <Upload class="h-8 w-8 mx-auto" />
            </div>
            <div :class="['upload-text', isDark ? 'text-gray-300' : 'text-gray-700']">
              <p class="text-sm font-medium">点击上传文件</p>
              <p class="text-xs mt-1">或将文件拖拽到此处</p>
            </div>
            <div v-if="accept" :class="['upload-hint text-xs mt-2', isDark ? 'text-gray-500' : 'text-gray-400']">
              支持格式: {{ acceptText }}
            </div>
            <div v-if="maxSize" :class="['upload-hint text-xs', isDark ? 'text-gray-500' : 'text-gray-400']">
              最大文件大小: {{ formatFileSize(maxSize) }}
            </div>
          </div>
          
          <div v-else class="upload-progress">
            <div :class="['upload-icon mb-3', isDark ? 'text-blue-400' : 'text-blue-500']">
              <Loader2 v-if="!isPaused" class="h-8 w-8 mx-auto animate-spin" />
              <Upload v-else class="h-8 w-8 mx-auto" />
            </div>
            <div :class="['upload-text', isDark ? 'text-gray-300' : 'text-gray-700']">
              <p class="text-sm font-medium">{{ isPaused ? '上传已暂停' : '正在上传...' }}</p>
              <div class="mt-2">
                <a-progress
                  :percent="uploadProgress"
                  :show-text="false"
                  size="small"
                  :color="isDark ? '#3b82f6' : undefined"
                />
                <div class="flex items-center justify-between mt-1">
                  <p class="text-xs">{{ uploadProgress }}%</p>
                  <div class="flex space-x-2">
                    <a-button
                      v-if="!isPaused"
                      size="mini"
                      type="text"
                      @click="pauseUpload"
                    >
                      暂停
                    </a-button>
                    <a-button
                      v-else
                      size="mini"
                      type="primary"
                      @click="resumeUpload"
                    >
                      继续
                    </a-button>
                    <a-button
                      size="mini"
                      type="text"
                      status="danger"
                      @click="cancelUpload"
                    >
                      取消
                    </a-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </a-upload>
    
    <!-- 已上传文件显示 -->
    <div v-if="fileInfo && !uploading" class="uploaded-file mt-3">
      <div
        :class="[
          'flex items-center justify-between p-3 rounded-lg border',
          isDark ? 'border-gray-600 bg-gray-800/50' : 'border-gray-200 bg-gray-50'
        ]"
      >
        <div class="flex items-center space-x-3">
          <div :class="['file-icon', isDark ? 'text-green-400' : 'text-green-500']">
            <FileCheck class="h-5 w-5" />
          </div>
          <div>
            <p :class="['text-sm font-medium', isDark ? 'text-gray-200' : 'text-gray-900']">
              {{ fileInfo.name }}
            </p>
            <p :class="['text-xs', isDark ? 'text-gray-400' : 'text-gray-500']">
              {{ formatFileSize(fileInfo.size) }}
            </p>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <a-button
            size="small"
            type="text"
            :class="isDark ? 'text-gray-400 hover:text-gray-200' : 'text-gray-500 hover:text-gray-700'"
            @click="clearFile"
          >
            <template #icon>
              <X class="h-4 w-4" />
            </template>
          </a-button>
        </div>
      </div>
    </div>
    
    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message mt-2">
      <p :class="['text-sm', isDark ? 'text-red-400' : 'text-red-600']">
        {{ errorMessage }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Upload, Loader2, FileCheck, X } from 'lucide-vue-next'
import { useThemeStore } from '@/stores/themeStore'
import { uploadFile, validateFileType, validateFileSize, formatFileSize, TusUploadManager } from '@/utils/fileUpload'
import type { FileUploadOptions, UploadResult } from '@/utils/fileUpload'
import { smartParseApk, extractInfoFromFilename } from '@/utils/apkParser'
import type { ApkInfo } from '@/utils/apkParser'

interface Props {
  accept?: string
  maxSize?: number // 字节
  modelValue?: string // 下载地址
  enableApkParsing?: boolean // 是否启用 APK 解析
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'upload-success', result: UploadResult): void
  (e: 'upload-error', error: Error): void
  (e: 'apk-parsed', info: ApkInfo): void // APK 解析完成事件
}

const props = withDefaults(defineProps<Props>(), {
  accept: '',
  maxSize: 100 * 1024 * 1024 // 默认100MB
})

const emit = defineEmits<Emits>()

const themeStore = useThemeStore()
const isDark = computed(() => themeStore.isDark)

const uploading = ref(false)
const uploadProgress = ref(0)
const fileInfo = ref<{ name: string; size: number } | null>(null)
const errorMessage = ref('')
const isPaused = ref(false)
const uploadManager = ref<TusUploadManager | null>(null)

const acceptText = computed(() => {
  if (!props.accept) return '所有文件'
  const types = props.accept.split(',').map(type => type.trim())
  // 将文件扩展名转换为更友好的显示格式
  const friendlyTypes = types.map(type => {
    if (type.startsWith('.')) {
      return type.toUpperCase() // .apk -> APK
    }
    return type
  })
  return friendlyTypes.join(', ')
})

const beforeUpload = async (file: File) => {
  console.log('beforeUpload file:', file)
  errorMessage.value = ''

  // 检查文件对象是否有效
  if (!file || !file.name) {
    console.error('Invalid file in beforeUpload:', file)
    errorMessage.value = '无效的文件对象'
    return false
  }

  // 验证文件类型
  if (props.accept) {
    const allowedTypes = props.accept.split(',').map(type => type.trim())
    if (!validateFileType(file, allowedTypes)) {
      errorMessage.value = `不支持的文件类型，请选择 ${acceptText.value} 格式的文件`
      return false
    }
  }

  // 验证文件大小
  if (props.maxSize && !validateFileSize(file, props.maxSize)) {
    errorMessage.value = `文件大小超过限制，最大允许 ${formatFileSize(props.maxSize)}`
    return false
  }

  // 如果是 APK 文件且启用了解析功能，则解析 APK 信息
  if (props.enableApkParsing && file.name.toLowerCase().endsWith('.apk')) {
    try {
      console.log('开始解析 APK 文件...')

      // 显示解析提示（可选）
      const parseStartTime = Date.now()

      const apkInfo = await parseApkFile(file)

      const parseTime = Date.now() - parseStartTime
      console.log(`APK 解析成功，耗时 ${parseTime}ms:`, apkInfo)

      emit('apk-parsed', apkInfo)
    } catch (error) {
      console.warn('APK 解析失败，尝试从文件名提取信息:', error)

      // 如果解析失败，尝试从文件名提取信息
      const fallbackInfo = extractInfoFromFilename(file.name)
      if (fallbackInfo.versionName || fallbackInfo.versionCode) {
        console.log('使用文件名解析结果:', fallbackInfo)
        emit('apk-parsed', {
          packageName: 'unknown',
          versionName: fallbackInfo.versionName || '1.0.0',
          versionCode: fallbackInfo.versionCode || '1',
          label: fallbackInfo.label || 'Unknown App'
        })
      } else {
        console.warn('文件名解析也失败，跳过 APK 信息提取')
      }
    }
  }

  return true
}

// APK 解析函数
const parseApkFile = async (file: File): Promise<ApkInfo> => {
  try {
    // 使用智能解析器
    return await smartParseApk(file)
  } catch (error) {
    console.warn('智能解析失败，使用文件名备用方案:', error)
    // 备用方案：从文件名提取信息
    const fallbackInfo = extractInfoFromFilename(file.name)
    return {
      packageName: 'unknown',
      versionName: fallbackInfo.versionName || '1.0.0',
      versionCode: fallbackInfo.versionCode || '1',
      label: fallbackInfo.label || file.name.replace('.apk', '')
    }
  }
}

const handleUpload = async (option: any) => {
  console.log('handleUpload option:', option)

  // 从不同的可能结构中提取文件对象
  const file = option.file || option.fileItem?.file || option

  if (!file || !file.name) {
    console.error('Invalid file object:', file)
    errorMessage.value = '无效的文件对象'
    return
  }

  uploading.value = true
  uploadProgress.value = 0
  errorMessage.value = ''
  isPaused.value = false

  try {
    const uploadOptions: FileUploadOptions = {
      fileName: file.name,
      mimeType: file.type,
      onProgress: (progress) => {
        uploadProgress.value = Math.min(progress, 100) // 确保进度不超过100%
      },
      onComplete: (fileId) => {
        console.log('Upload complete, fileId:', fileId)
      },
      onError: (error) => {
        console.error('Upload error:', error)
      }
    }

    const result = await uploadFile(file, uploadOptions)

    // 保存文件信息
    fileInfo.value = {
      name: file.name,
      size: file.size
    }

    // 更新下载地址
    emit('update:modelValue', result.downloadUrl)
    emit('upload-success', result)

    // 不在这里显示成功消息，让父组件处理
  } catch (error) {
    console.error('Upload failed:', error)
    errorMessage.value = error instanceof Error ? error.message : '上传失败'
    emit('upload-error', error as Error)
    // 不在这里显示错误消息，让父组件处理
  } finally {
    uploading.value = false
    uploadProgress.value = 0
    isPaused.value = false
    uploadManager.value = null
  }
}

const clearFile = () => {
  fileInfo.value = null
  errorMessage.value = ''
  isPaused.value = false
  uploadManager.value = null
  emit('update:modelValue', '')
}

const pauseUpload = () => {
  if (uploadManager.value) {
    uploadManager.value.pause()
    isPaused.value = true
  }
}

const resumeUpload = () => {
  if (uploadManager.value) {
    uploadManager.value.resume()
    isPaused.value = false
  }
}

const cancelUpload = () => {
  if (uploadManager.value) {
    uploadManager.value.cancel()
    uploading.value = false
    uploadProgress.value = 0
    isPaused.value = false
    uploadManager.value = null
    errorMessage.value = '上传已取消'
  }
}
</script>

<style scoped lang="less">
.file-upload-container {
  .upload-area {
    cursor: pointer;
    position: relative;

    &:hover:not(.uploading) {
      transform: translateY(-1px);
    }

    // 确保上传区域高度固定，防止抖动
    &.uploading {
      cursor: not-allowed;
    }
  }

  .upload-content,
  .upload-progress {
    pointer-events: none;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .uploaded-file {
    .file-icon {
      flex-shrink: 0;
    }
  }
}
</style>
