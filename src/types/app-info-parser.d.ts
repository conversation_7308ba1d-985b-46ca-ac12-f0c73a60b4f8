/**
 * Type declarations for app-info-parser library
 */

declare module 'app-info-parser' {
  interface ParseResult {
    package: string
    versionName: string
    versionCode: number | string
    label: string
    icon?: string
    usesSdk?: {
      minSdkVersion?: string | number
      targetSdkVersion?: string | number
    }
    [key: string]: any
  }

  class AppInfoParser {
    constructor(file: File | Buffer | string)
    parse(): Promise<ParseResult>
  }

  export default AppInfoParser
}
