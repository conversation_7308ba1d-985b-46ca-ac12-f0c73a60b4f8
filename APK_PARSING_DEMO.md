# APK 自动解析功能演示

## 功能概述

现在当您上传 Android APK 文件时，系统会自动解析 APK 中的应用信息，并将 `versionName` 和 `versionCode` 自动回填到表单中。

## 支持的解析方式

### 1. 智能文件名解析
系统可以从 APK 文件名中智能提取版本信息，支持以下格式：

- `MyApp-v1.2.3.apk` → versionName: "1.2.3", versionCode: "1"
- `MyApp_2.0.1_release.apk` → versionName: "2.0.1", versionCode: "1"
- `app-1.5.0-build123.apk` → versionName: "1.5.0", versionCode: "123"
- `WeChat_v8.0.32_build2056.apk` → versionName: "8.0.32", versionCode: "2056"

### 2. APK 结构验证
系统会验证上传的文件是否为有效的 APK：
- 检查是否包含 `AndroidManifest.xml`
- 检查是否包含 `classes.dex`
- 验证 ZIP 文件结构

## 使用流程

### 1. 选择 Android 平台
首先在发布页面选择 Android 平台的应用，这会：
- 限制文件类型只能上传 `.apk` 文件
- 启用 APK 自动解析功能

### 2. 上传 APK 文件
当您上传 APK 文件时：
1. 系统首先验证文件类型和大小
2. 自动解析 APK 文件结构
3. 提取版本信息
4. 自动填充到表单字段

### 3. 自动填充表单
解析成功后：
- `版本名称` 字段自动填充 `versionName`
- `版本号` 字段自动填充 `versionCode`
- 清除相关字段的验证错误
- 显示成功提示消息

## 测试示例

您可以创建以下格式的测试文件来验证功能：

```bash
# 创建测试 APK 文件（实际上是空文件，仅用于测试文件名解析）
touch "MyApp-v2.1.0-build456.apk"
touch "WeChat_v8.0.32_release.apk"
touch "TikTok-v1.5.3.apk"
```

## 错误处理

### 1. 解析失败时
如果 APK 解析失败，系统会：
- 尝试从文件名提取信息
- 使用默认值填充（versionName: "1.0.0", versionCode: "1"）
- 显示警告信息但不阻止上传

### 2. 无效文件时
如果上传的不是有效 APK 文件：
- 显示文件类型错误
- 阻止文件上传
- 提示用户选择正确的文件格式

## 技术实现

### 核心文件
- `src/utils/apkParser.ts` - APK 解析工具
- `src/components/FileUpload.vue` - 文件上传组件
- `src/views/publish/index.vue` - 发布页面

### 关键功能
1. **智能文件名解析**: 使用正则表达式提取版本信息
2. **APK 结构验证**: 使用 JSZip 验证文件结构
3. **自动表单填充**: 解析成功后自动更新表单字段
4. **错误处理**: 多层级的错误处理和备用方案

## 使用建议

### 1. 文件命名规范
为了获得最佳的解析效果，建议 APK 文件命名遵循以下规范：
- 包含应用名称
- 使用 `-` 或 `_` 分隔
- 明确标注版本号（如 v1.2.3 或 1.2.3）
- 可选的构建号（如 build123）

### 2. 版本号格式
- versionName: 建议使用语义化版本号（如 1.2.3）
- versionCode: 建议使用递增的整数

### 3. 验证结果
上传后请验证自动填充的信息是否正确，必要时可以手动调整。

## 演示步骤

1. 访问 http://localhost:8082/
2. 选择一个 Android 平台的应用
3. 点击"创建发布"
4. 在"应用包文件"区域上传 APK 文件
5. 观察版本信息自动填充
6. 查看控制台日志了解解析过程

## 扩展功能

未来可以扩展的功能：
1. 解析更多 APK 信息（包名、权限、图标等）
2. 支持其他平台文件解析（IPA、DMG、EXE）
3. 版本冲突检测
4. 自动生成更新日志

现在您可以在浏览器中测试这个功能了！
